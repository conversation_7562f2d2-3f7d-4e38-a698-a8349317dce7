#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
员工结构数据清洗脚本
功能：清洗员工结构2011_2023.xlsx文件，筛选2014-2024年数据，去除空值
作者：数据清洗脚本
日期：2025年
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def 清洗员工结构数据():
    """
    清洗员工结构数据的主函数
    """
    # 输入文件路径
    输入文件路径 = "csv_data/员工结构2011_2023.xlsx"
    输出文件路径 = "csv_data/清洗后_员工结构_2014_2024.xlsx"
    
    print("开始清洗员工结构数据...")
    print(f"输入文件：{输入文件路径}")
    
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        数据框 = pd.read_excel(输入文件路径)
        
        print(f"原始数据形状：{数据框.shape}")
        print(f"原始数据列名：{list(数据框.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n原始数据前5行：")
        print(数据框.head())
        
        # 检查是否有年份列
        年份列名 = None
        可能的年份列名 = ['年份', 'year', 'Year', 'YEAR', '年', 'date', 'Date', 'DATE']
        
        for 列名 in 可能的年份列名:
            if 列名 in 数据框.columns:
                年份列名 = 列名
                break
        
        if 年份列名 is None:
            # 如果没有找到年份列，检查是否有包含年份信息的列
            for 列名 in 数据框.columns:
                if any(str(year) in str(列名) for year in range(2011, 2025)):
                    print(f"发现可能的年份相关列：{列名}")
            
            print("未找到明确的年份列，请检查数据结构")
            print("可用的列名：", list(数据框.columns))
            return
        
        print(f"找到年份列：{年份列名}")
        
        # 转换年份列为数值类型
        数据框[年份列名] = pd.to_numeric(数据框[年份列名], errors='coerce')
        
        # 筛选2014-2024年的数据
        print("正在筛选2014-2024年的数据...")
        筛选后数据 = 数据框[(数据框[年份列名] >= 2014) & (数据框[年份列名] <= 2024)]
        
        print(f"筛选后数据形状：{筛选后数据.shape}")
        
        # 清除空值
        print("正在清除空值...")
        清洗前行数 = len(筛选后数据)
        
        # 删除所有列都为空的行
        筛选后数据 = 筛选后数据.dropna(how='all')
        
        # 删除关键列为空的行（年份列不能为空）
        筛选后数据 = 筛选后数据.dropna(subset=[年份列名])
        
        清洗后行数 = len(筛选后数据)
        删除行数 = 清洗前行数 - 清洗后行数
        
        print(f"清洗前行数：{清洗前行数}")
        print(f"清洗后行数：{清洗后行数}")
        print(f"删除空值行数：{删除行数}")
        
        # 显示每年的数据量
        print("\n各年份数据统计：")
        年份统计 = 筛选后数据[年份列名].value_counts().sort_index()
        for 年份, 数量 in 年份统计.items():
            print(f"{int(年份)}年：{数量}条记录")
        
        # 显示清洗后的数据信息
        print(f"\n清洗后数据信息：")
        print(f"数据形状：{筛选后数据.shape}")
        print(f"数据类型：")
        print(筛选后数据.dtypes)
        
        # 检查各列的空值情况
        print(f"\n各列空值统计：")
        空值统计 = 筛选后数据.isnull().sum()
        for 列名, 空值数量 in 空值统计.items():
            if 空值数量 > 0:
                空值比例 = (空值数量 / len(筛选后数据)) * 100
                print(f"{列名}：{空值数量}个空值 ({空值比例:.2f}%)")
        
        # 保存清洗后的数据
        print(f"\n正在保存清洗后的数据到：{输出文件路径}")
        筛选后数据.to_excel(输出文件路径, index=False)
        
        # 生成数据清洗报告
        报告文件路径 = "csv_data/员工结构数据清洗报告.txt"
        生成清洗报告(输入文件路径, 输出文件路径, 数据框, 筛选后数据, 年份列名, 报告文件路径)
        
        print(f"数据清洗完成！")
        print(f"清洗后文件：{输出文件路径}")
        print(f"清洗报告：{报告文件路径}")
        
        return 筛选后数据
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {输入文件路径}")
        print("请确认文件路径是否正确")
    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

def 生成清洗报告(输入文件, 输出文件, 原始数据, 清洗后数据, 年份列名, 报告文件路径):
    """
    生成数据清洗报告
    """
    with open(报告文件路径, 'w', encoding='utf-8') as f:
        f.write("员工结构数据清洗报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"清洗时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"输入文件：{输入文件}\n")
        f.write(f"输出文件：{输出文件}\n\n")
        
        f.write("数据清洗概况：\n")
        f.write(f"原始数据行数：{len(原始数据)}\n")
        f.write(f"原始数据列数：{len(原始数据.columns)}\n")
        f.write(f"清洗后数据行数：{len(清洗后数据)}\n")
        f.write(f"清洗后数据列数：{len(清洗后数据.columns)}\n")
        f.write(f"删除行数：{len(原始数据) - len(清洗后数据)}\n\n")
        
        f.write("筛选条件：\n")
        f.write("- 年份范围：2014-2024年\n")
        f.write("- 删除所有列都为空的行\n")
        f.write(f"- 删除{年份列名}列为空的行\n\n")
        
        f.write("各年份数据统计：\n")
        年份统计 = 清洗后数据[年份列名].value_counts().sort_index()
        for 年份, 数量 in 年份统计.items():
            f.write(f"{int(年份)}年：{数量}条记录\n")
        
        f.write("\n各列空值统计：\n")
        空值统计 = 清洗后数据.isnull().sum()
        for 列名, 空值数量 in 空值统计.items():
            if 空值数量 > 0:
                空值比例 = (空值数量 / len(清洗后数据)) * 100
                f.write(f"{列名}：{空值数量}个空值 ({空值比例:.2f}%)\n")

if __name__ == "__main__":
    清洗员工结构数据()
