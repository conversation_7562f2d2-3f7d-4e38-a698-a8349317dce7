#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿色专利数据清洗脚本
功能：对单个年份的上市公司、子公司绿色专利数据进行清洗，不进行合并
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def 加载单个数据文件(文件路径):
    """
    加载单个绿色专利数据文件

    参数:
        文件路径 (str): 数据文件的路径

    返回:
        pandas.DataFrame: 加载的数据
    """
    print(f"开始加载数据文件: {文件路径}")

    if not os.path.exists(文件路径):
        raise FileNotFoundError(f"数据文件不存在: {文件路径}")

    try:
        数据框 = pd.read_excel(文件路径)
        print(f"数据加载成功，共 {len(数据框)} 条记录")
        return 数据框
    except Exception as e:
        raise Exception(f"加载数据文件时出错: {e}")

def 批量处理数据文件(数据目录='csv_data'):
    """
    批量处理2014-2023年的绿色专利数据文件，分别清洗但不合并

    参数:
        数据目录 (str): 数据文件所在目录
    """
    print("开始批量处理数据文件...")

    处理结果 = {}

    # 遍历2014-2023年的数据文件
    for 年份 in range(2014, 2024):
        文件路径 = os.path.join(数据目录, f'上市公司、子公司绿色专利数据{年份}.xlsx')

        if os.path.exists(文件路径):
            print(f"\n正在处理 {年份} 年数据...")
            try:
                # 加载数据
                原始数据 = 加载单个数据文件(文件路径)

                # 清洗数据
                清洗后数据 = 清洗数据(原始数据, 年份)

                # 保存清洗后的数据
                输出文件路径 = os.path.join(数据目录, f'清洗后_绿色专利数据_{年份}.xlsx')
                保存清洗数据(清洗后数据, 输出文件路径, 年份)

                处理结果[年份] = {
                    '原始记录数': len(原始数据),
                    '清洗后记录数': len(清洗后数据),
                    '输出文件': 输出文件路径
                }

            except Exception as e:
                print(f"  - 处理{年份}年数据时出错: {e}")
                处理结果[年份] = {'错误': str(e)}
        else:
            print(f"  - {年份}年数据文件不存在: {文件路径}")

    return 处理结果

def 清洗数据(数据框, 年份=None):
    """
    数据清洗函数 - 使用中文命名，重点清理无效空数据

    参数:
        数据框 (pandas.DataFrame): 需要清洗的数据
        年份 (int): 数据年份，可选

    返回:
        pandas.DataFrame: 清洗后的数据
    """
    print(f"\n开始数据清洗{f'（{年份}年）' if 年份 else ''}...")
    原始记录数 = len(数据框)

    # 1. 清理列名中的空格和特殊字符
    print("1. 清理列名...")
    数据框.columns = 数据框.columns.str.strip()
    # 修正可能的列名问题
    列名映射 = {
        '关联股票代 码': '关联股票代码',
        '专利英 文名称': '专利英文名称',
        '国际 申请号': '国际申请号',
        '申请 人': '申请人',
        '专利 类型': '专利类型'
    }
    数据框 = 数据框.rename(columns=列名映射)

    # 2. 删除完全空白的行
    print("2. 删除完全空白的行...")
    清理前行数 = len(数据框)
    数据框 = 数据框.dropna(how='all')
    清理后行数 = len(数据框)
    print(f"   删除了 {清理前行数 - 清理后行数} 条完全空白的记录")

    # 3. 删除完全重复的记录
    print("3. 删除重复记录...")
    去重前记录数 = len(数据框)
    数据框 = 数据框.drop_duplicates()
    去重后记录数 = len(数据框)
    print(f"   删除了 {去重前记录数 - 去重后记录数} 条重复记录")

    # 4. 深度清理无效空数据
    print("4. 深度清理无效空数据...")

    # 清理文本字段中的无效值
    文本字段列表 = ['关联企业名称', '申请人', '专利中文名称', '专利英文名称', '发明人', '代理机构']
    for 列名 in 文本字段列表:
        if 列名 in 数据框.columns:
            # 转换为字符串并去除空格
            数据框[列名] = 数据框[列名].astype(str).str.strip()
            # 将各种形式的空值转换为NaN
            无效值列表 = ['nan', 'NaN', 'NULL', 'null', 'None', '', '无', '空', '-', '--', '/', '\\', 'N/A', 'n/a']
            数据框[列名] = 数据框[列名].replace(无效值列表, np.nan)
            # 清理只包含特殊字符的值
            数据框[列名] = 数据框[列名].replace(r'^[^\w\u4e00-\u9fff]+$', np.nan, regex=True)

    # 5. 删除关键字段全部为空的记录
    print("5. 删除关键字段全部为空的记录...")
    关键字段 = ['专利申请号', '申请人', '专利中文名称']
    关键字段清理前 = len(数据框)
    数据框 = 数据框.dropna(subset=关键字段, how='all')
    关键字段清理后 = len(数据框)
    print(f"   删除关键字段全为空的记录: {关键字段清理前 - 关键字段清理后} 条")

    # 6. 删除专利申请号为空的记录（专利申请号是最重要的标识）
    print("6. 删除专利申请号为空的记录...")
    if '专利申请号' in 数据框.columns:
        申请号清理前 = len(数据框)
        数据框 = 数据框.dropna(subset=['专利申请号'])
        申请号清理后 = len(数据框)
        print(f"   删除专利申请号为空的记录: {申请号清理前 - 申请号清理后} 条")

    # 7. 数据类型转换和标准化
    print("7. 数据类型转换和标准化...")

    # 处理日期字段
    日期字段 = ['申请日期', '公开日期', '授权日期', '最早优先权日期']
    for 列名 in 日期字段:
        if 列名 in 数据框.columns:
            # 将数字格式的日期转换为标准日期格式
            数据框[列名] = pd.to_numeric(数据框[列名], errors='coerce')
            # 假设日期格式为YYYYMMDD
            数据框[列名] = 数据框[列名].apply(
                lambda x: pd.to_datetime(str(int(x)), format='%Y%m%d', errors='coerce')
                if pd.notna(x) and len(str(int(x))) == 8 else np.nan
            )

    # 处理年份字段
    年份字段 = ['申请年份', '公开年份', '授权年份']
    for 列名 in 年份字段:
        if 列名 in 数据框.columns:
            数据框[列名] = pd.to_numeric(数据框[列名], errors='coerce')

    # 8. 数据验证和异常值处理
    print("8. 数据验证和异常值处理...")

    # 检查年份的合理性
    当前年份 = datetime.now().year
    for 列名 in 年份字段:
        if 列名 in 数据框.columns:
            # 删除年份不合理的记录（如小于1980或大于当前年份）
            年份清理前 = len(数据框)
            数据框 = 数据框[(数据框[列名].isna()) | ((数据框[列名] >= 1980) & (数据框[列名] <= 当前年份))]
            年份清理后 = len(数据框)
            if 年份清理前 != 年份清理后:
                print(f"   删除{列名}异常的记录: {年份清理前 - 年份清理后} 条")

    # 9. 股票代码标准化
    print("9. 股票代码标准化...")
    if '关联股票代码' in 数据框.columns:
        # 去除股票代码中的非数字字符，保留6位数字
        数据框['关联股票代码'] = 数据框['关联股票代码'].astype(str).str.extract('(\d{6})')
        # 将无效的股票代码设为NaN
        数据框.loc[数据框['关联股票代码'].str.len() != 6, '关联股票代码'] = np.nan

    # 10. 最终数据质量检查
    print("10. 最终数据质量检查...")
    # 显示缺失值统计
    缺失值统计 = 数据框.isnull().sum()
    print("   各列缺失值统计:")
    for 列名, 缺失数量 in 缺失值统计.items():
        if 缺失数量 > 0:
            缺失百分比 = (缺失数量 / len(数据框)) * 100
            print(f"     {列名}: {缺失数量} ({缺失百分比:.2f}%)")

    print(f"\n数据清洗完成！")
    print(f"原始记录数: {原始记录数:,}")
    print(f"清洗后记录数: {len(数据框):,}")
    print(f"删除记录数: {原始记录数 - len(数据框):,}")
    print(f"数据保留率: {(len(数据框) / 原始记录数) * 100:.2f}%")

    return 数据框

def 保存清洗数据(数据框, 输出文件路径, 年份=None):
    """
    保存清洗后的数据

    参数:
        数据框 (pandas.DataFrame): 清洗后的数据
        输出文件路径 (str): 输出文件路径
        年份 (int): 数据年份，可选
    """
    print(f"\n正在保存清洗后的数据到: {输出文件路径}")

    try:
        # 确保输出目录存在
        输出目录 = os.path.dirname(输出文件路径)
        if 输出目录 and not os.path.exists(输出目录):
            os.makedirs(输出目录)

        # 保存Excel文件
        数据框.to_excel(输出文件路径, index=False)
        print("Excel文件保存成功！")

        # 同时保存CSV格式
        csv文件路径 = 输出文件路径.replace('.xlsx', '.csv')
        数据框.to_csv(csv文件路径, index=False, encoding='utf-8-sig')
        print(f"CSV文件保存成功: {csv文件路径}")

        # 生成数据摘要报告
        生成数据摘要报告(数据框, 年份)

    except Exception as e:
        print(f"保存数据时出错: {e}")
        raise

def 生成数据摘要报告(数据框, 年份=None):
    """
    生成数据摘要报告

    参数:
        数据框 (pandas.DataFrame): 数据
        年份 (int): 数据年份，可选
    """
    print("\n" + "="*60)
    print(f"数据摘要报告{f'（{年份}年）' if 年份 else ''}")
    print("="*60)

    # 基本统计
    print(f"总记录数: {len(数据框):,}")
    print(f"总列数: {len(数据框.columns)}")

    # 按专利类型统计
    if '专利类型' in 数据框.columns:
        print("\n按专利类型分布:")
        专利类型统计 = 数据框['专利类型'].value_counts()
        for 专利类型, 数量 in 专利类型统计.head(10).items():
            if pd.notna(专利类型):
                print(f"  {专利类型}: {数量:,} 条")

    # 按绿色专利大类统计
    if '绿色专利大类' in 数据框.columns:
        print("\n按绿色专利大类分布:")
        大类统计 = 数据框['绿色专利大类'].value_counts()
        for 大类, 数量 in 大类统计.head(10).items():
            if pd.notna(大类):
                print(f"  {大类}: {数量:,} 条")

    # 数据质量统计
    print("\n数据质量统计:")
    总单元格数 = len(数据框) * len(数据框.columns)
    缺失单元格数 = 数据框.isnull().sum().sum()
    完整度 = ((总单元格数 - 缺失单元格数) / 总单元格数) * 100
    print(f"  数据完整度: {完整度:.2f}%")
    print(f"  缺失值总数: {缺失单元格数:,}")

    # 关键字段完整性
    关键字段 = ['专利申请号', '申请人', '专利中文名称', '关联企业名称']
    print("\n关键字段完整性:")
    for 字段 in 关键字段:
        if 字段 in 数据框.columns:
            非空数量 = 数据框[字段].notna().sum()
            完整率 = (非空数量 / len(数据框)) * 100
            print(f"  {字段}: {非空数量:,}/{len(数据框):,} ({完整率:.2f}%)")

def 处理单个文件(文件路径):
    """
    处理单个绿色专利数据文件

    参数:
        文件路径 (str): 数据文件路径
    """
    try:
        print(f"\n{'='*80}")
        print(f"开始处理文件: {文件路径}")
        print(f"{'='*80}")

        # 1. 加载数据
        原始数据 = 加载单个数据文件(文件路径)

        # 2. 提取年份信息
        文件名 = os.path.basename(文件路径)
        年份 = None
        for year in range(2014, 2024):
            if str(year) in 文件名:
                年份 = year
                break

        # 3. 数据清洗
        清洗后数据 = 清洗数据(原始数据, 年份)

        # 4. 生成输出文件路径
        文件目录 = os.path.dirname(文件路径)
        输出文件名 = f'清洗后_绿色专利数据_{年份}.xlsx' if 年份 else '清洗后_绿色专利数据.xlsx'
        输出文件路径 = os.path.join(文件目录, 输出文件名)

        # 5. 保存清洗后的数据
        保存清洗数据(清洗后数据, 输出文件路径, 年份)

        print(f"\n文件处理完成: {文件路径}")
        return True

    except Exception as e:
        print(f"处理文件时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def 主函数():
    """
    主函数 - 可以选择批量处理或单个文件处理
    """
    print("绿色专利数据清洗工具")
    print("="*50)
    print("1. 批量处理所有年份数据文件")
    print("2. 处理单个数据文件")
    print("3. 退出")

    while True:
        选择 = input("\n请选择操作模式 (1/2/3): ").strip()

        if 选择 == '1':
            # 批量处理模式
            try:
                处理结果 = 批量处理数据文件()

                print("\n" + "="*60)
                print("批量处理结果汇总")
                print("="*60)

                成功数量 = 0
                失败数量 = 0

                for 年份, 结果 in 处理结果.items():
                    if '错误' in 结果:
                        print(f"{年份}年: 处理失败 - {结果['错误']}")
                        失败数量 += 1
                    else:
                        print(f"{年份}年: 处理成功")
                        print(f"  原始记录: {结果['原始记录数']:,} 条")
                        print(f"  清洗后记录: {结果['清洗后记录数']:,} 条")
                        print(f"  输出文件: {结果['输出文件']}")
                        成功数量 += 1

                print(f"\n处理完成！成功: {成功数量} 个文件，失败: {失败数量} 个文件")

            except Exception as e:
                print(f"批量处理过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            break

        elif 选择 == '2':
            # 单个文件处理模式
            文件路径 = input("请输入数据文件路径: ").strip()
            if 文件路径:
                处理单个文件(文件路径)
            break

        elif 选择 == '3':
            print("退出程序")
            break

        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    主函数()
