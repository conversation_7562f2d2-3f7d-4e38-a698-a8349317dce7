#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政府补助数据清洗脚本
功能：从政府补助.xlsx文件中提取2014年到2023年的数据
作者：数据清洗脚本
日期：2025年
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def 清洗政府补助数据():
    """
    清洗政府补助数据，只保留2014年到2023年的数据
    """
    try:
        # 读取原始数据文件
        输入文件路径 = 'csv_data/政府补助.xlsx'
        输出文件路径 = 'csv_data/清洗后_政府补助_2014_2023.xlsx'
        
        print(f"正在读取文件: {输入文件路径}")
        
        # 读取Excel文件
        原始数据 = pd.read_excel(输入文件路径)
        
        print(f"原始数据形状: {原始数据.shape}")
        print(f"原始数据列名: {list(原始数据.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n原始数据前5行:")
        print(原始数据.head())
        
        # 查找年份相关的列
        年份列 = None
        可能的年份列名 = ['年份', 'year', 'Year', 'YEAR', '年', '报告年度', '会计年度', 'date', 'Date']
        
        for 列名 in 原始数据.columns:
            if any(年份关键词 in str(列名) for 年份关键词 in 可能的年份列名):
                年份列 = 列名
                break
        
        # 如果没有找到明确的年份列，尝试从其他列中提取年份信息
        if 年份列 is None:
            print("未找到明确的年份列，正在尝试从数据中识别年份信息...")
            
            # 检查每一列是否包含年份信息
            for 列名 in 原始数据.columns:
                样本数据 = 原始数据[列名].dropna().head(10)
                for 值 in 样本数据:
                    if isinstance(值, (int, float)) and 2000 <= 值 <= 2030:
                        年份列 = 列名
                        print(f"在列 '{列名}' 中发现年份信息")
                        break
                    elif isinstance(值, str) and any(str(年) in 值 for 年 in range(2000, 2030)):
                        年份列 = 列名
                        print(f"在列 '{列名}' 中发现年份信息")
                        break
                if 年份列:
                    break
        
        if 年份列 is None:
            print("警告：未能识别年份列，将显示所有列的信息供手动选择")
            print("\n数据列信息:")
            for i, 列名 in enumerate(原始数据.columns):
                样本值 = 原始数据[列名].dropna().head(3).tolist()
                print(f"{i}: {列名} - 样本值: {样本值}")
            return
        
        print(f"使用年份列: {年份列}")
        
        # 提取年份信息
        def 提取年份(值):
            """从值中提取年份"""
            if pd.isna(值):
                return None
            
            if isinstance(值, (int, float)):
                if 2000 <= 值 <= 2030:
                    return int(值)
            elif isinstance(值, str):
                # 尝试提取4位数年份
                import re
                年份匹配 = re.findall(r'20\d{2}', 值)
                if 年份匹配:
                    return int(年份匹配[0])
            
            return None
        
        # 创建年份列
        原始数据['提取年份'] = 原始数据[年份列].apply(提取年份)
        
        # 显示年份分布
        年份分布 = 原始数据['提取年份'].value_counts().sort_index()
        print(f"\n年份分布:")
        print(年份分布)
        
        # 筛选2014年到2023年的数据
        筛选条件 = (原始数据['提取年份'] >= 2014) & (原始数据['提取年份'] <= 2023)
        清洗后数据 = 原始数据[筛选条件].copy()
        
        # 删除临时的提取年份列
        清洗后数据 = 清洗后数据.drop('提取年份', axis=1)
        
        print(f"\n清洗后数据形状: {清洗后数据.shape}")
        print(f"保留的年份范围: 2014-2023")
        
        # 显示清洗后的年份分布
        清洗后数据['年份检查'] = 原始数据.loc[筛选条件, 年份列].apply(提取年份)
        清洗后年份分布 = 清洗后数据['年份检查'].value_counts().sort_index()
        print(f"\n清洗后年份分布:")
        print(清洗后年份分布)
        清洗后数据 = 清洗后数据.drop('年份检查', axis=1)
        
        # 保存清洗后的数据
        清洗后数据.to_excel(输出文件路径, index=False)
        print(f"\n清洗后的数据已保存到: {输出文件路径}")
        
        # 生成数据清洗报告
        报告内容 = f"""
政府补助数据清洗报告
=====================

清洗时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
输入文件: {输入文件路径}
输出文件: {输出文件路径}

数据概况:
- 原始数据行数: {原始数据.shape[0]}
- 原始数据列数: {原始数据.shape[1]}
- 清洗后数据行数: {清洗后数据.shape[0]}
- 清洗后数据列数: {清洗后数据.shape[1]}
- 数据保留率: {清洗后数据.shape[0]/原始数据.shape[0]*100:.2f}%

年份筛选条件: 2014年 <= 年份 <= 2023年
使用的年份列: {年份列}

清洗后年份分布:
{清洗后年份分布.to_string()}
        """
        
        # 保存报告
        报告文件路径 = 'csv_data/政府补助数据清洗报告.txt'
        with open(报告文件路径, 'w', encoding='utf-8') as f:
            f.write(报告内容)
        
        print(f"\n数据清洗报告已保存到: {报告文件路径}")
        print("\n数据清洗完成！")
        
        return 清洗后数据
        
    except Exception as e:
        print(f"数据清洗过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def 主函数():
    """主函数"""
    print("开始政府补助数据清洗...")
    print("=" * 50)
    
    # 检查输入文件是否存在
    if not os.path.exists('csv_data/政府补助.xlsx'):
        print("错误：找不到输入文件 'csv_data/政府补助.xlsx'")
        return
    
    # 执行数据清洗
    结果 = 清洗政府补助数据()
    
    if 结果 is not None:
        print("\n✅ 数据清洗成功完成！")
    else:
        print("\n❌ 数据清洗失败！")

if __name__ == "__main__":
    主函数()
